import csv
from pathlib import Path

from django.db import migrations

MIGRATIONS_FOLDER = Path(__file__).parent
DATA_FOLDER = MIGRATIONS_FOLDER.parent / "migrations_data" / "0037_defect_mapping_one_substandard"


def create_features(apps, schema_editor):
    Feature = apps.get_model('defects', 'Feature')
    csv_path = DATA_FOLDER / 'Feature.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        features = [
            Feature(
                id=int(row['id']),
                key=row['key'],
                display_name=row['display_name']
            )
            for row in reader
        ]
        Feature.objects.bulk_create(features)


def reverse_create_features(apps, schema_editor):
    Feature = apps.get_model('your_app', 'Feature')
    csv_path = DATA_FOLDER / 'Feature.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            Feature.objects.filter(
                id=int(row['id']),
                key=row['key'],
                display_name=row['display_name']
            ).delete()


def create_standard_features(apps, schema_editor):
    StandardFeature = apps.get_model('defects', 'StandardFeature')
    Standard = apps.get_model('defects', 'Standard')
    Feature = apps.get_model('defects', 'Feature')
    csv_path = DATA_FOLDER / 'StandardFeature.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        standard_features = [
            StandardFeature(
                id=int(row['id']),
                standard=Standard.objects.get(id=int(row['standard'])),
                feature_id=Feature.objects.get(id=int(row['feature'])),
                display_name=row['display_name']
            )
            for row in reader
        ]
        StandardFeature.objects.bulk_create(standard_features)


def reverse_create_standard_features(apps, schema_editor):
    StandardFeature = apps.get_model('defects', 'StandardFeature')
    Standard = apps.get_model('defects', 'Standard')
    Feature = apps.get_model('defects', 'Feature')
    csv_path = DATA_FOLDER / 'StandardFeature.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            StandardFeature.objects.filter(
                id=int(row['id']),
                standard=Standard.objects.get(id=int(row['standard'])),
                feature_id=Feature.objects.get(id=int(row['feature'])),
                display_name=row['display_name']
            ).delete()


def create_sub_features(apps, schema_editor):
    SubFeature = apps.get_model('defects', 'SubFeature')
    Feature = apps.get_model('defects', 'Feature')
    csv_path = DATA_FOLDER / 'SubFeature.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        sub_features = [
            SubFeature(
                id=int(row['id']),
                key=row['key'],
                display_name=row['display_name'],
                display_order=int(row['display_order']),
                feature=Feature.objects.get(id=int(row['feature'])),
                kind=row['kind'],
                numeric_kind=row['numeric_kind']
            )
            for row in reader
        ]
        SubFeature.objects.bulk_create(sub_features)


def reverse_create_sub_features(apps, schema_editor):
    SubFeature = apps.get_model('defects', 'SubFeature')
    Feature = apps.get_model('defects', 'Feature')
    csv_path = DATA_FOLDER / 'SubFeature.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            SubFeature.objects.filter(
                id=int(row['id']),
                key=row['key'],
                display_name=row['display_name'],
                display_order=int(row['display_order']),
                feature=Feature.objects.get(id=int(row['feature'])),
                kind=row['kind'],
                numeric_kind=row['numeric_kind']
            ).delete()


def create_sub_feature_options(apps, schema_editor):
    SubFeatureOption = apps.get_model('defects', 'SubFeatureOption')
    SubFeature = apps.get_model('defects', 'SubFeature')
    csv_path = DATA_FOLDER / 'SubFeatureOption.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        sub_feature_options = [
            SubFeatureOption(
                id=int(row['id']),
                key=row['key'],
                sub_feature=SubFeature.objects.get(id=int(row['sub_feature'])),
                display_name=row['display_name'],
                display_order=int(row['display_order'])
            )
            for row in reader
        ]
        SubFeatureOption.objects.bulk_create(sub_feature_options)


def reverse_create_sub_feature_options(apps, schema_editor):
    SubFeatureOption = apps.get_model('defects', 'SubFeatureOption')
    SubFeature = apps.get_model('defects', 'SubFeature')
    csv_path = DATA_FOLDER / 'SubFeatureOption.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            SubFeatureOption.objects.filter(
                id=int(row['id']),
                key=row['key'],
                sub_feature=SubFeature.objects.get(id=int(row['sub_feature'])),
                display_name=row['display_name'],
                display_order=int(row['display_order'])
            ).delete()


def create_standard_sub_features(apps, schema_editor):
    StandardSubFeature = apps.get_model('defects', 'StandardSubFeature')
    Standard = apps.get_model('defects', 'Standard')
    SubFeature = apps.get_model('defects', 'SubFeature')
    SubFeatureOption = apps.get_model('defects', 'SubFeatureOption')
    csv_path = DATA_FOLDER / 'StandardSubFeature.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        standard_sub_features = [
            StandardSubFeature(
                id=int(row['id']),
                standard=Standard.objects.get(id=int(row['standard'])),
                sub_feature=SubFeature.objects.get(id=int(row['sub_feature'])),
                display_name=row['display_name'],
                display_order=int(row['display_order']),
                default_option=SubFeatureOption.objects.get(id=int(row['default_option'])) if row['default_option'] else None,
                numeric_min=float(row['numeric_min']) if row['numeric_min'] else None,
                numeric_max=float(row['numeric_max']) if row['numeric_max'] else None,
                numeric_unit=row['numeric_unit'] if row['numeric_unit'] else None,
                numeric_default=float(row['numeric_default']) if row['numeric_default'] else None,
                numeric_display_decimal_places=int(row['numeric_display_decimal_places']) if row['numeric_display_decimal_places'] else None,
                quantity_field_number=int(row['quantity_field_number']) if row['quantity_field_number'] else None,
                characteristic_field_number=int(row['characteristic_field_number']) if row['characteristic_field_number'] else None,
                maps_numeric_ranges_to_labels=row['maps_numeric_ranges_to_labels'] == 'TRUE'
            )
            for row in reader
        ]
        StandardSubFeature.objects.bulk_create(standard_sub_features)


def reverse_create_standard_sub_features(apps, schema_editor):
    StandardSubFeature = apps.get_model('defects', 'StandardSubFeature')
    Standard = apps.get_model('defects', 'Standard')
    SubFeature = apps.get_model('defects', 'SubFeature')
    SubFeatureOption = apps.get_model('defects', 'SubFeatureOption')
    csv_path = DATA_FOLDER / 'StandardSubFeature.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            StandardSubFeature.objects.filter(
                id=int(row['id']),
                standard=Standard.objects.get(id=int(row['standard'])),
                sub_feature=SubFeature.objects.get(id=int(row['sub_feature'])),
                display_name=row['display_name'],
                display_order=int(row['display_order']),
                default_option=SubFeatureOption.objects.get(id=int(row['default_option'])) if row['default_option'] else None,
                numeric_min=float(row['numeric_min']) if row['numeric_min'] else None,
                numeric_max=float(row['numeric_max']) if row['numeric_max'] else None,
                numeric_unit=row['numeric_unit'] if row['numeric_unit'] else None,
                numeric_default=float(row['numeric_default']) if row['numeric_default'] else None,
                numeric_display_decimal_places=int(row['numeric_display_decimal_places']) if row['numeric_display_decimal_places'] else None,
                quantity_field_number=int(row['quantity_field_number']) if row['quantity_field_number'] else None,
                characteristic_field_number=int(row['characteristic_field_number']) if row['characteristic_field_number'] else None,
                maps_numeric_ranges_to_labels=row['maps_numeric_ranges_to_labels'] == 'TRUE'
            ).delete()


def create_codes(apps, schema_editor):
    Code = apps.get_model('defects', 'Code')
    Standard = apps.get_model('defects', 'Standard')
    Feature = apps.get_model('defects', 'Feature')
    StandardSubCategory = apps.get_model('defects', 'StandardSubcategory')
    CodeStandardSubcategory = apps.get_model('defects', 'CodeStandardSubcategory')
    csv_path = DATA_FOLDER / 'Code.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = list(csv.DictReader(csvfile))

    codes = []
    for row in reader:
        code = Code(
            id=int(row['id']),
            standard=Standard.objects.get(id=int(row['standard'])),
            feature=Feature.objects.get(id=int(row['feature'])),
            code_type=row['code_type'],
            display_code=row['display_code'],
            display_name=row['display_name'],
            at_joint_allowed=row['at_joint_allowed'] == 'TRUE',
            continuous_required=row['continuous_required'] == 'TRUE',
            continuous_allowed=row['continuous_allowed'] == 'TRUE',
            remarks_required=row['remarks_required'] == 'TRUE',
            clock_position_from_required=row['clock_position_from_required'] == 'TRUE',
            clock_position_from_min=int(row['clock_position_from_min']),
            clock_position_from_max=int(row['clock_position_from_max']),
            clock_position_to_required=row['clock_position_to_required'] == 'TRUE',
            clock_position_to_min=int(row['clock_position_to_min']),
            clock_position_to_max=int(row['clock_position_to_max']),
            is_start_code=row['is_start_code'] == 'TRUE',
            is_end_code=row['is_end_code'] == 'TRUE'
        )
        codes.append(code)
    Code.objects.bulk_create(codes)

    code_subcategories = []
    for row in reader:
        code = Code.objects.get(id=int(row['id']))
        for s in row['standard_subcategories'].split(', '):
            standard_subcategory = StandardSubCategory.objects.get(id=int(s))
            code_subcategories.append(
                CodeStandardSubcategory(
                    code=code,
                    standard_subcategory=standard_subcategory
                )
            )
    CodeStandardSubcategory.objects.bulk_create(code_subcategories)


def reverse_create_codes(apps, schema_editor):
    Code = apps.get_model('defects', 'Code')
    Standard = apps.get_model('defects', 'Standard')
    Feature = apps.get_model('defects', 'Feature')
    CodeStandardSubcategory = apps.get_model('defects', 'CodeStandardSubcategory')
    csv_path = DATA_FOLDER / 'Code.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:  
            code = Code.objects.filter(
                id=int(row['id']),
                standard=Standard.objects.get(id=int(row['standard'])),
                feature=Feature.objects.get(id=int(row['feature'])),
                code_type=row['code_type'],
                display_code=row['display_code'],
                display_name=row['display_name'],
                at_joint_allowed=row['at_joint_allowed'] == 'TRUE',
                continuous_required=row['continuous_required'] == 'TRUE',
                continuous_allowed=row['continuous_allowed'] == 'TRUE',
                remarks_required=row['remarks_required'] == 'TRUE',
                clock_position_from_required=row['clock_position_from_required'] == 'TRUE',
                clock_position_from_min=int(row['clock_position_from_min']),
                clock_position_from_max=int(row['clock_position_from_max']),
                clock_position_to_required=row['clock_position_to_required'] == 'TRUE',
                clock_position_to_min=int(row['clock_position_to_min']),
                clock_position_to_max=int(row['clock_position_to_max']),
                is_start_code=row['is_start_code'] == 'TRUE',
                is_end_code=row['is_end_code'] == 'TRUE'
            ).first()
            CodeStandardSubcategory.objects.filter(code=code).delete()
            code.delete()


def create_code_requirements(apps, schema_editor):
    CodeRequirement = apps.get_model('defects', 'CodeRequirement')
    Code = apps.get_model('defects', 'Code')
    SubFeature = apps.get_model('defects', 'SubFeature')
    SubFeatureOption = apps.get_model('defects', 'SubFeatureOption')
    csv_path = DATA_FOLDER / 'CodeRequirement.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        code_requirements = [
            CodeRequirement(
                id=int(row['id']),
                code=Code.objects.get(id=int(row['code'])),
                sub_feature=SubFeature.objects.get(id=int(row['sub_feature'])),
                required_option=SubFeatureOption.objects.get(id=int(row['required_option'])) if row['required_option'] else None,
                numeric_option_range_min=float(row['numeric_option_range_min']) if row['numeric_option_range_min'] else None,
                numeric_option_range_max=float(row['numeric_option_range_max']) if row['numeric_option_range_max'] else None
            )
            for row in reader
        ]
        CodeRequirement.objects.bulk_create(code_requirements)


def reverse_create_code_requirements(apps, schema_editor):
    CodeRequirement = apps.get_model('defects', 'CodeRequirement')
    Code = apps.get_model('defects', 'Code')
    SubFeature = apps.get_model('defects', 'SubFeature')
    SubFeatureOption = apps.get_model('defects', 'SubFeatureOption')
    csv_path = DATA_FOLDER / 'CodeRequirement.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            CodeRequirement.objects.filter(
                id=int(row['id']),
                code=Code.objects.get(id=int(row['code'])),
                sub_feature=SubFeature.objects.get(id=int(row['sub_feature'])),
                required_option=SubFeatureOption.objects.get(id=int(row['required_option'])) if row['required_option'] else None,
                numeric_option_range_min=float(row['numeric_option_range_min']) if row['numeric_option_range_min'] else None,
                numeric_option_range_max=float(row['numeric_option_range_max']) if row['numeric_option_range_max'] else None
            ).delete()


def create_code_scores(apps, schema_editor):
    CodeScore = apps.get_model('defects', 'CodeScore')
    Code = apps.get_model('defects', 'Code')
    StandardSubCategory = apps.get_model('defects', 'StandardSubcategory')
    CodeScoreStandardSubcategory = apps.get_model('defects', 'CodeScoreStandardSubcategory')
    csv_path = DATA_FOLDER / 'CodeScore.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = list(csv.DictReader(csvfile))

    code_scores = []
    for row in reader:
        code_score = CodeScore(
            id=int(row['id']),
            code=Code.objects.get(id=int(row['code'])),
            service_score=float(row['service_score']) if row['service_score'] else None,
            structural_score=float(row['structural_score']) if row['structural_score'] else None,
            repair_priority=float(row['repair_priority']) if row['repair_priority'] else None,
            required_clock_position_min=int(row['required_clock_position_min']) if row['required_clock_position_min'] else None,
            required_clock_position_max=int(row['required_clock_position_max']) if row['required_clock_position_max'] else None,
            required_clock_position_min_spread=int(row['required_clock_position_min_spread']) if row['required_clock_position_min_spread'] else None,
            required_clock_position_max_spread=int(row['required_clock_position_max_spread']) if row['required_clock_position_max_spread'] else None,
            added_service_score_per_metre=float(row['added_service_score_per_metre']) if row['added_service_score_per_metre'] else None,
            added_structural_score_per_metre=float(row['added_structural_score_per_metre']) if row['added_structural_score_per_metre'] else None
        )
        code_scores.append(code_score)
    CodeScore.objects.bulk_create(code_scores)

    code_score_subcategories = []
    for row in reader:
        code_score = CodeScore.objects.get(id=int(row['id']))
        for s in row['standard_subcategories'].split(', '):
            standard_subcategory = StandardSubCategory.objects.get(id=int(s))
            code_score_subcategories.append(
                CodeScoreStandardSubcategory(
                    code_score=code_score,
                    standard_subcategory=standard_subcategory
                )
            )
    CodeScoreStandardSubcategory.objects.bulk_create(code_score_subcategories)


def reverse_create_code_scores(apps, schema_editor):
    CodeScore = apps.get_model('defects', 'CodeScore')
    CodeScoreStandardSubcategory = apps.get_model('defects', 'CodeScoreStandardSubcategory')
    Code = apps.get_model('defects', 'Code')
    csv_path = DATA_FOLDER / 'CodeScore.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            code_score = CodeScore.objects.filter(
                id=int(row['id']),
                code=Code.objects.get(id=int(row['code'])),
                service_score=float(row['service_score']) if row['service_score'] else None,
                structural_score=float(row['structural_score']) if row['structural_score'] else None,
                repair_priority=float(row['repair_priority']) if row['repair_priority'] else None,
                required_clock_position_min=int(row['required_clock_position_min']) if row['required_clock_position_min'] else None,
                required_clock_position_max=int(row['required_clock_position_max']) if row['required_clock_position_max'] else None,
                required_clock_position_min_spread=int(row['required_clock_position_min_spread']) if row['required_clock_position_min_spread'] else None,
                required_clock_position_max_spread=int(row['required_clock_position_max_spread']) if row['required_clock_position_max_spread'] else None,
                added_service_score_per_metre=float(row['added_service_score_per_metre']) if row['added_service_score_per_metre'] else None,
                added_structural_score_per_metre=float(row['added_structural_score_per_metre']) if row['added_structural_score_per_metre'] else None
            ).first()
            CodeScoreStandardSubcategory.objects.filter(code_score=code_score).delete()
            code_score.delete()


def create_code_score_requirements(apps, schema_editor):
    CodeScoreRequirement = apps.get_model('defects', 'CodeScoreRequirement')
    CodeScore = apps.get_model('defects', 'CodeScore')
    SubFeature = apps.get_model('defects', 'SubFeature')
    SubFeatureOption = apps.get_model('defects', 'SubFeatureOption')
    csv_path = DATA_FOLDER / 'CodeScoreRequirement.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        code_score_requirements = [
            CodeScoreRequirement(
                id=int(row['id']),
                code_score=CodeScore.objects.get(id=int(row['code_score'])),
                sub_feature=SubFeature.objects.get(id=int(row['sub_feature'])),
                required_option=SubFeatureOption.objects.get(id=int(row['required_option'])) if row['required_option'] else None,
                numeric_option_min_breakpoint=float(row['numeric_option_min_breakpoint']) if row['numeric_option_min_breakpoint'] else None,
                numeric_option_max_breakpoint=float(row['numeric_option_max_breakpoint']) if row['numeric_option_max_breakpoint'] else None
            )
            for row in reader
        ]
        CodeScoreRequirement.objects.bulk_create(code_score_requirements)


def reverse_create_code_score_requirements(apps, schema_editor):
    CodeScoreRequirement = apps.get_model('defects', 'CodeScoreRequirement')
    CodeScore = apps.get_model('defects', 'CodeScore')
    SubFeature = apps.get_model('defects', 'SubFeature')
    SubFeatureOption = apps.get_model('defects', 'SubFeatureOption')
    csv_path = DATA_FOLDER / 'CodeScoreRequirement.csv'

    with csv_path.open(newline='', encoding='utf-8') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            CodeScoreRequirement.objects.filter(
                id=int(row['id']),
                code_score=CodeScore.objects.get(id=int(row['code_score'])),
                sub_feature=SubFeature.objects.get(id=int(row['sub_feature'])),
                required_option=SubFeatureOption.objects.get(id=int(row['required_option'])) if row['required_option'] else None,
                numeric_option_min_breakpoint=float(row['numeric_option_min_breakpoint']) if row['numeric_option_min_breakpoint'] else None,
                numeric_option_max_breakpoint=float(row['numeric_option_max_breakpoint']) if row['numeric_option_max_breakpoint'] else None
            ).delete()


def fail(apps, schema_editor):
    raise Exception("Force failure for testing")


class Migration(migrations.Migration):

    dependencies = [
        (
            "defects",
            "0036_rename_at_joint_required_code_at_joint_allowed",
        ),
    ]

    operations = [
        migrations.RunPython(create_features, reverse_create_features),
        migrations.RunPython(create_standard_features, reverse_create_standard_features),
        migrations.RunPython(create_sub_features, reverse_create_sub_features),
        migrations.RunPython(create_sub_feature_options, reverse_create_sub_feature_options),
        migrations.RunPython(create_standard_sub_features, reverse_create_standard_sub_features),
        migrations.RunPython(create_codes, reverse_create_codes),
        migrations.RunPython(create_code_requirements, reverse_create_code_requirements),
        migrations.RunPython(create_code_scores, reverse_create_code_scores),
        migrations.RunPython(create_code_score_requirements, reverse_create_code_score_requirements),
        migrations.RunPython(fail, migrations.RunPython.noop),
    ]
